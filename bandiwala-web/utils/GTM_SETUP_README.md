# Google Tag Manager (GTM) Setup for Bandiwala

## Overview
The GTM implementation has been fixed and improved with proper TypeScript types and better error handling. The system now supports both Google Tag Manager (GTM) and Google Analytics 4 (GA4) tracking.

## Files Fixed/Created

### 1. `utils/gtm.ts` - Main GTM utility (FIXED)
- ✅ Fixed TypeScript strict mode violations
- ✅ Removed `any` types and added proper type definitions
- ✅ Added support for both GTM dataLayer and GA4 gtag
- ✅ Created comprehensive event tracking functions
- ✅ Exported proper TypeScript interfaces

### 2. `utils/gtm-examples.ts` - Usage examples (NEW)
- Example implementations for common e-commerce events
- Shows how to integrate with your existing cart and order systems

### 3. `components/GTMScript.tsx` - GTM script component (NEW)
- Proper Next.js Script component implementation
- Includes both script and noscript fallback

## Current Setup Status

### ✅ What's Working
- Google Analytics 4 (GA4) is configured in `layout.tsx`
- GTM utility functions are ready to use
- TypeScript types are properly defined

### ⚠️ What Needs Configuration
- GTM container ID is not set in environment variables
- GTM script is not added to the layout

## Setup Instructions

### Option 1: Use GTM (Recommended for advanced tracking)

1. **Get your GTM Container ID**
   - Go to [Google Tag Manager](https://tagmanager.google.com/)
   - Create a container or use existing one
   - Copy your GTM ID (format: GTM-XXXXXXX)

2. **Add GTM ID to environment variables**
   ```bash
   # Add to your .env file
   NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
   ```

3. **Update layout.tsx to include GTM**
   ```tsx
   import { GTMScript, GTMNoscript } from '@/components/GTMScript';
   
   // In your layout.tsx head section, add:
   <GTMScript gtmId={process.env.NEXT_PUBLIC_GTM_ID!} />
   
   // In your body section, add:
   <GTMNoscript gtmId={process.env.NEXT_PUBLIC_GTM_ID!} />
   ```

### Option 2: Keep using GA4 only (Current setup)
- Your current GA4 setup will continue working
- The GTM functions will automatically use `gtag` if dataLayer is not available

## Usage Examples

### Track Add to Cart
```tsx
import { gtmAddToCart } from '@/utils/gtm';

const handleAddToCart = (dish: any) => {
  // Your existing cart logic...
  
  // Track the event
  gtmAddToCart({
    item_id: dish.id,
    item_name: dish.name,
    price: dish.price,
    quantity: 1,
    category: 'food',
    brand: dish.vendorName
  });
};
```

### Track Purchase
```tsx
import { gtmPurchase } from '@/utils/gtm';

const handleOrderComplete = (orderId: string, cartItems: any[], total: number) => {
  // Your existing order logic...
  
  // Track the purchase
  gtmPurchase(orderId, {
    total: total,
    items: cartItems.map(item => ({
      item_id: item.id,
      item_name: item.name,
      price: item.price,
      quantity: item.quantity,
      category: 'food',
      brand: item.vendorName
    })),
    currency: 'INR'
  });
};
```

### Track Search
```tsx
import { gtmSearch } from '@/utils/gtm';

const handleSearch = (query: string) => {
  // Your existing search logic...
  
  // Track the search
  gtmSearch(query);
};
```

## Available Functions

- `gtmAddToCart(item)` - Track add to cart events
- `gtmRemoveFromCart(item)` - Track remove from cart events
- `gtmBeginCheckout(cart)` - Track checkout start
- `gtmPurchase(transactionId, cart)` - Track completed purchases
- `gtmViewItem(item)` - Track item views
- `gtmSearch(searchTerm)` - Track search events
- `gtmLogin(method)` - Track user login
- `gtmSignUp(method)` - Track user registration
- `gtmEvent(event, params)` - Generic event tracking

## TypeScript Types

All functions are fully typed. Import types as needed:

```tsx
import type { GTMItem, GTMCartData, GTMEvent } from '@/utils/gtm';
```

## Testing

1. **Check browser console** - No TypeScript errors should appear
2. **Use browser dev tools** - Check Network tab for GTM/GA requests
3. **GTM Preview mode** - Use GTM's preview feature to test events
4. **GA4 Real-time reports** - Check if events appear in GA4

## Migration Notes

- All previous `any` types have been replaced with proper TypeScript interfaces
- The generic `gtmEvent` function now supports both GTM and GA4
- Example functions have been removed to avoid unused code warnings
- The implementation is backward compatible with your existing GA4 setup

## Next Steps

1. Decide whether to implement GTM or stick with GA4
2. If using GTM, follow the setup instructions above
3. Start implementing event tracking in your components using the provided examples
4. Test the implementation thoroughly before deploying to production
