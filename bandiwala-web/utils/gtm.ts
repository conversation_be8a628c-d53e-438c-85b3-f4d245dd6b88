export const gtmEvent = (event: string, params: Record<string, any> = {}) => {
  if (typeof window !== 'undefined' && (window as any).dataLayer) {
    (window as any).dataLayer.push({ event, ...params });
  }
};

// Example usage for Add to Cart
const handleAddToCart = (item) => {
  // ...existing add to cart logic...
  gtmEvent('add_to_cart', {
    item_id: item.id,
    item_name: item.name,
    price: item.price,
    quantity: 1
  });
};

// Example usage for Checkout Started
const handleCheckoutStarted = (cart) => {
  // ...existing checkout logic...
  gtmEvent('begin_checkout', {
    value: cart.total,
    currency: 'INR',
    items: cart.items
  });
};
